"""
MQTT Module for Waffles Backend

This module provides MQTT client functionality for handling device commands
and responses. It integrates with the existing FastAPI application and
supports various device operations through MQTT messaging.

Key Components:
- AsyncMQTTClient: Main MQTT client implementation
- Message handlers: Command processing and response generation
- Configuration integration: Uses settings from config.py and .env

Usage:
    The MQTT client is automatically initialized when the application starts
    if MQTT is enabled in the configuration. It listens for commands on
    topics matching the pattern: devices/{client_id}/commands/electronic/{command}
    
    Responses are published to: devices/{client_id}/responses/electronic/{command}
"""

from .client import (
    AsyncMQTTClient,
    get_mqtt_client,
    initialize_mqtt_client,
    shutdown_mqtt_client,
    mqtt_client
)

from .handlers import (
    handle_device_command,
    execute_command,
    register_mqtt_handlers,
    handle_unlock_command,
    handle_unlock_service_command,
    handle_start_command,
    handle_stop_command,
    handle_status_command,
    handle_ping_command,
    handle_hardware_command,
    handle_storage_command
)

from .service import (
    MQTTService,
    mqtt_service,
    publish_door_event,
    publish_transaction_event,
    publish_storage_event,
    publish_error_event
)

from .timeline_integration import (
    log_mqtt_command_to_timeline,
    log_mqtt_event_to_timeline,
    log_door_event_to_timeline,
    log_unlock_event_to_timeline
)

__all__ = [
    # Client
    "AsyncMQTTClient",
    "get_mqtt_client",
    "initialize_mqtt_client",
    "shutdown_mqtt_client",
    "mqtt_client",

    # Handlers
    "handle_device_command",
    "execute_command",
    "register_mqtt_handlers",
    "handle_unlock_command",
    "handle_unlock_service_command",
    "handle_start_command",
    "handle_stop_command",
    "handle_status_command",
    "handle_ping_command",
    "handle_hardware_command",
    "handle_storage_command",

    # Service
    "MQTTService",
    "mqtt_service",
    "publish_door_event",
    "publish_transaction_event",
    "publish_storage_event",
    "publish_error_event",

    # Timeline Integration
    "log_mqtt_command_to_timeline",
    "log_mqtt_event_to_timeline",
    "log_door_event_to_timeline",
    "log_unlock_event_to_timeline"
]
