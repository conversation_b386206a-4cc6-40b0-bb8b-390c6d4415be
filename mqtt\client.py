import asyncio
import json
import logging
from typing import Optional, Dict, Any, Callable
import paho.mqtt.client as mqtt
from config import device_config

logger = logging.getLogger(__name__)


class AsyncMQTTClient:
    """Async MQTT client for handling device commands and responses."""
    
    def __init__(self):
        self.config = device_config.mqtt_config
        self.client: Optional[mqtt.Client] = None
        self.is_connected = False
        self.is_subscribed = False
        self.main_loop: Optional[asyncio.AbstractEventLoop] = None
        self.subscription_mids: Dict[int, str] = {}
        self.messages_queue: asyncio.Queue = asyncio.Queue()
        self.message_handlers: Dict[str, Callable] = {}
        
        # Validate required configuration
        self._validate_config()
        
    def _validate_config(self):
        """Validate MQTT configuration."""
        required_fields = ["host", "username", "password", "client_id"]
        for field in required_fields:
            if not self.config.get(field):
                raise ValueError(f"MQTT configuration missing required field: {field}")
    
    def set_main_loop(self, loop: asyncio.AbstractEventLoop):
        """Set the main event loop reference."""
        self.main_loop = loop
        logger.info("Main event loop reference set for MQTT client")
        
    def register_message_handler(self, topic_pattern: str, handler: Callable):
        """Register a message handler for a specific topic pattern."""
        self.message_handlers[topic_pattern] = handler
        logger.info(f"Registered message handler for topic pattern: {topic_pattern}")
        
    def _setup_client(self):
        """Setup MQTT client with callbacks."""
        self.client = mqtt.Client(client_id=self.config["client_id"])
        
        # Set authentication
        if self.config["username"] and self.config["password"]:
            self.client.username_pw_set(self.config["username"], self.config["password"])
            
        # Set callbacks
        self.client.on_connect = self._on_connect
        self.client.on_message = self._on_message
        self.client.on_disconnect = self._on_disconnect
        self.client.on_subscribe = self._on_subscribe
        
    def _on_connect(self, client, userdata, flags, rc):
        """Callback for when the client connects to the broker."""
        if rc == 0:
            self.is_connected = True
            logger.info(f"Connected to MQTT broker successfully")
            logger.info(f"   Broker: {self.config['host']}:{self.config['port']}")
            logger.info(f"   Client ID: {self.config['client_id']}")
            logger.info(f"   Username: {self.config['username']}")
            
            # Subscribe to command topics
            self._subscribe_to_topics()
            
        else:
            self.is_connected = False
            logger.error(f"Failed to connect to MQTT broker with result code {rc}")
            
    def _subscribe_to_topics(self):
        """Subscribe to relevant MQTT topics."""
        # Subscribe to electronic command topics for this device
        client_id = self.config["client_id"]
        command_topic = f"devices/{client_id}/electronic/#"

        logger.info(f"Subscribing to command topics...")
        result, mid = self.client.subscribe(command_topic)
        self.subscription_mids[mid] = command_topic
        logger.info(f"   Message ID {mid}: {command_topic}")
        
    def _on_subscribe(self, client, userdata, mid, granted_qos):
        """Callback for when subscription is acknowledged."""
        topic = self.subscription_mids.get(mid, f"Unknown topic (MID: {mid})")
        logger.info(f"Subscription callback received:")
        logger.info(f"   Message ID: {mid}")
        logger.info(f"   Topic: {topic}")
        logger.info(f"   Granted QoS: {granted_qos}")
        
        # Check if subscription was successful
        if granted_qos and len(granted_qos) > 0:
            qos = granted_qos[0]
            if qos == 128:
                logger.error(f"Subscription FAILED for: {topic}")
                logger.error(f"   QoS 128 indicates failure - topic pattern invalid or access denied")
            elif 0 <= qos <= 2:
                logger.info(f"Subscription SUCCESS for: {topic}")
                logger.info(f"   QoS: {qos} - topic is now active and listening")
                # Mark as subscribed if ANY subscription succeeds
                if not self.is_subscribed:
                    self.is_subscribed = True
                    logger.info("First successful subscription - client is now ready!")
            else:
                logger.warning(f"Subscription UNCLEAR for: {topic}")
                logger.warning(f"   QoS: {qos} - unexpected value")
        else:
            logger.error(f"Subscription FAILED for: {topic}")
            logger.error(f"   No QoS granted")
            
    def _on_disconnect(self, client, userdata, rc):
        """Callback for when the client disconnects."""
        self.is_connected = False
        self.is_subscribed = False
        logger.warning(f"Disconnected from MQTT broker (rc={rc})")
        
    def _on_message(self, client, userdata, msg):
        """Callback for when a message is received."""
        logger.debug(f"Raw MQTT message received")
        logger.debug(f"   Connected: {self.is_connected}")
        logger.debug(f"   Subscribed: {self.is_subscribed}")
        
        # Handle topic display safely
        topic = msg.topic.decode() if isinstance(msg.topic, bytes) else msg.topic
        logger.debug(f"   Topic: {topic}")
        
        if self.is_connected and self.is_subscribed:
            logger.debug("Processing message (fully ready)")
            # Use the stored main loop reference to schedule the coroutine
            if self.main_loop and self.main_loop.is_running():
                try:
                    asyncio.run_coroutine_threadsafe(
                        self._process_message(msg), self.main_loop
                    )
                    logger.debug("Message processing scheduled in main event loop")
                except Exception as e:
                    logger.error(f"Error scheduling message processing: {e}")
            else:
                logger.warning("Main loop not available, cannot process message")
        else:
            logger.debug("Message received but not fully ready yet")
            # Queue message for later processing
            if self.main_loop and self.main_loop.is_running():
                try:
                    asyncio.run_coroutine_threadsafe(
                        self._queue_message_async(msg), self.main_loop
                    )
                    logger.debug("Message queued for later processing")
                except Exception as e:
                    logger.error(f"Error queuing message: {e}")
                    
    async def _process_message(self, msg):
        """Process received MQTT message."""
        try:
            # Handle topic and payload decoding
            topic = msg.topic.decode() if isinstance(msg.topic, bytes) else msg.topic
            payload = msg.payload.decode() if isinstance(msg.payload, bytes) else (msg.payload or "")
            
            logger.info(f"MQTT Message received:")
            logger.info(f"   Topic: {topic}")
            logger.info(f"   Payload: {payload}")
            logger.info(f"   QoS: {msg.qos}")
            logger.info(f"   Retain: {msg.retain}")
            
            # Find matching handler
            logger.debug(f"Looking for handler among {len(self.message_handlers)} registered handlers")
            for pattern, handler in self.message_handlers.items():
                logger.debug(f"Checking pattern: {pattern} against topic: {topic}")
                if self._topic_matches_pattern(topic, pattern):
                    logger.info(f"Found matching handler for pattern: {pattern}")
                    try:
                        await handler(topic, payload, msg)
                        return
                    except Exception as e:
                        logger.error(f"Error in message handler for pattern {pattern}: {e}")

            logger.warning(f"No handler found for topic: {topic}")
            logger.debug(f"Registered patterns: {list(self.message_handlers.keys())}")
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            logger.error(f"   Topic type: {type(msg.topic)}, value: {msg.topic}")
            logger.error(f"   Payload type: {type(msg.payload)}, value: {msg.payload}")
            
    def _topic_matches_pattern(self, topic: str, pattern: str) -> bool:
        """Check if topic matches pattern (simple wildcard support)."""
        # Handle multi-level wildcard (#)
        if "#" in pattern:
            # Split pattern and topic into parts
            pattern_parts = pattern.split("/")
            topic_parts = topic.split("/")

            # Find the position of # in pattern
            hash_index = -1
            for i, part in enumerate(pattern_parts):
                if part == "#":
                    hash_index = i
                    break

            if hash_index >= 0:
                # Check parts before #
                if len(topic_parts) < hash_index:
                    return False

                for i in range(hash_index):
                    pattern_part = pattern_parts[i]
                    if i >= len(topic_parts):
                        return False
                    topic_part = topic_parts[i]

                    if pattern_part != "+" and pattern_part != topic_part:
                        return False

                # # matches everything after this point
                return True

        # Handle single-level wildcard (+)
        elif "+" in pattern:
            pattern_parts = pattern.split("/")
            topic_parts = topic.split("/")

            if len(pattern_parts) != len(topic_parts):
                return False

            for p_part, t_part in zip(pattern_parts, topic_parts):
                if p_part != "+" and p_part != t_part:
                    return False
            return True

        # Exact match
        else:
            return topic == pattern
            
    async def _queue_message_async(self, msg):
        """Queue message for later processing."""
        await self.messages_queue.put(msg)
        logger.debug(f"Message queued: {msg.topic}")
        
    async def _process_queued_messages(self):
        """Process any messages that arrived before we were fully ready."""
        if not self.messages_queue.empty():
            logger.info(f"Processing {self.messages_queue.qsize()} queued messages...")
            while not self.messages_queue.empty():
                msg = await self.messages_queue.get()
                logger.debug(f"Processing queued message: {msg.topic}")
                await self._process_message(msg)
                
    async def start(self):
        """Start the MQTT client."""
        if not self.config.get("enabled", True):
            logger.info("MQTT client is disabled in configuration")
            return
            
        logger.info(f"Starting MQTT client...")
        logger.info(f"   Broker: {self.config['host']}:{self.config['port']}")
        logger.info(f"   Client ID: {self.config['client_id']}")
        logger.info(f"   Username: {self.config['username']}")
        
        self._setup_client()
        
        try:
            self.client.connect(
                self.config["host"], 
                self.config["port"], 
                self.config.get("keepalive", 60)
            )
            self.client.loop_start()
            
            # Wait for connection and subscription
            timeout = self.config.get("connection_timeout", 15)
            start_time = asyncio.get_event_loop().time()
            
            while not (self.is_connected and self.is_subscribed):
                current_time = asyncio.get_event_loop().time()
                elapsed = current_time - start_time
                
                if elapsed > timeout:
                    logger.warning(f"Timeout waiting for full connection after {elapsed:.1f}s")
                    break
                    
                logger.debug(f"Waiting for connection... ({elapsed:.1f}s elapsed)")
                logger.debug(f"   Connected: {self.is_connected}")
                logger.debug(f"   Subscribed: {self.is_subscribed}")
                
                await asyncio.sleep(0.5)
            
            if self.is_connected and self.is_subscribed:
                logger.info("MQTT client is fully ready and listening for messages")
                await self._process_queued_messages()
            elif self.is_connected:
                logger.warning("Connected but subscription status unclear - continuing anyway")
                await self._process_queued_messages()
            else:
                logger.error("MQTT client failed to connect properly")
                
        except Exception as e:
            logger.error(f"Error starting MQTT client: {e}")
            raise
            
    async def stop(self):
        """Stop the MQTT client."""
        if self.client:
            logger.info("Stopping MQTT client...")
            self.client.loop_stop()
            self.client.disconnect()
            self.is_connected = False
            self.is_subscribed = False
            
    async def publish_response(self, client_id: str, command: str, response: Dict[str, Any]):
        """Publish response back to MQTT topic."""
        if not self.is_connected:
            logger.warning("Cannot publish response - not connected to MQTT broker")
            return False
            
        # Response topics go under responses/, not commands/
        response_topic = f"devices/{client_id}/responses/electronic/{command}"
        response_payload = json.dumps(response, ensure_ascii=False)
        
        try:
            info = self.client.publish(response_topic, response_payload, qos=1, retain=False)
            if info.rc == 0:
                # Wait for PUBACK when QoS=1 to confirm broker accepted it
                info.wait_for_publish()
                logger.info(f"Response published to: {response_topic}")
                logger.debug(f"   Payload: {response_payload}")
                logger.debug(f"   mid={getattr(info, 'mid', '?')} (acknowledged)")
                return True
            else:
                logger.error(f"Failed to publish response (client-side rc): {info.rc}")
                return False
        except Exception as e:
            logger.error(f"Error publishing response: {e}")
            return False


# Global MQTT client instance
mqtt_client: Optional[AsyncMQTTClient] = None


def get_mqtt_client() -> Optional[AsyncMQTTClient]:
    """Get the global MQTT client instance."""
    return mqtt_client


async def initialize_mqtt_client() -> Optional[AsyncMQTTClient]:
    """Initialize the global MQTT client."""
    global mqtt_client

    if not device_config.mqtt_config.get("enabled", True):
        logger.info("MQTT is disabled in configuration")
        return None

    try:
        mqtt_client = AsyncMQTTClient()

        # Set the main event loop reference
        main_loop = asyncio.get_running_loop()
        mqtt_client.set_main_loop(main_loop)

        # Register handlers before starting
        from mqtt.handlers import handle_device_command
        mqtt_client.register_message_handler(
            "devices/+/electronic/#",
            handle_device_command
        )
        logger.info("MQTT message handlers registered")

        await mqtt_client.start()
        return mqtt_client

    except Exception as e:
        logger.error(f"Failed to initialize MQTT client: {e}")
        return None


async def shutdown_mqtt_client():
    """Shutdown the global MQTT client."""
    global mqtt_client
    if mqtt_client:
        await mqtt_client.stop()
        mqtt_client = None
