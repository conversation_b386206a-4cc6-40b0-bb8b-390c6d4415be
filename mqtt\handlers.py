import asyncio
import logging
from typing import Dict, Any
from mqtt.client import get_mqtt_client

logger = logging.getLogger(__name__)


async def handle_device_command(topic: str, payload: str, msg) -> Dict[str, Any]:
    """
    Handle device commands received via MQTT.
    
    Expected topic format: devices/{client_id}/commands/electronic/{command}
    """
    logger.info(f"Handling device command from topic: {topic}")
    
    try:
        # Parse topic to extract client_id and command
        parts = topic.split("/")
        if len(parts) < 5 or parts[2] != "commands" or parts[3] != "electronic":
            logger.warning(f"Invalid command topic format: {topic}")
            return {
                "success": False,
                "error": "Invalid topic format",
                "topic": topic
            }
            
        client_id = parts[1]
        command = parts[4]
        
        if not command:
            logger.warning(f"No command found in topic: {topic}")
            return {
                "success": False,
                "error": "No command specified",
                "topic": topic
            }
            
        logger.info(f"Processing command '{command}' for client '{client_id}'")
        
        # Execute the command
        response = await execute_command(client_id, command, payload)
        
        # Publish response back via MQTT
        mqtt_client = get_mqtt_client()
        if mqtt_client:
            await mqtt_client.publish_response(client_id, command, response)
        else:
            logger.warning("MQTT client not available for response publishing")
            
        return response
        
    except Exception as e:
        logger.error(f"Error handling device command: {e}")
        return {
            "success": False,
            "error": str(e),
            "topic": topic
        }


async def execute_command(client_id: str, command: str, payload: str = "") -> Dict[str, Any]:
    """
    Execute a specific command and return the response.
    
    Args:
        client_id: The client ID from the MQTT topic
        command: The command to execute
        payload: Optional payload data
        
    Returns:
        Dict containing the command response
    """
    logger.info(f"Executing command '{command}' for client '{client_id}'")
    
    # Base response structure
    response = {
        "success": True,
        "client_id": client_id,
        "command": command,
        "timestamp": asyncio.get_event_loop().time()
    }
    
    try:
        if command == "unlock":
            response.update(await handle_unlock_command(client_id, payload))
            
        elif command == "unlock_service":
            response.update(await handle_unlock_service_command(client_id, payload))
            
        elif command == "start":
            response.update(await handle_start_command(client_id, payload))
            
        elif command == "stop":
            response.update(await handle_stop_command(client_id, payload))
            
        elif command == "status":
            response.update(await handle_status_command(client_id, payload))
            
        elif command == "ping":
            response.update(await handle_ping_command(client_id, payload))
            
        else:
            logger.warning(f"Unknown command: {command}")
            response.update({
                "success": False,
                "error": f"Unknown command: {command}",
                "available_commands": ["unlock", "unlock_service", "start", "stop", "status", "ping"]
            })
            
    except Exception as e:
        logger.error(f"Error executing command '{command}': {e}")
        response.update({
            "success": False,
            "error": str(e)
        })

    # Log command execution to timeline
    try:
        from mqtt.timeline_integration import log_mqtt_command_to_timeline, log_unlock_event_to_timeline

        # Determine operational mode from command
        mode = _determine_mode_from_command(command)

        # Log the command execution
        await log_mqtt_command_to_timeline(
            client_id=client_id,
            command=command,
            payload=payload,
            response=response,
            mode=mode
        )

        # Log specific unlock events
        if command in ["unlock", "unlock_service"] and response.get("success", True):
            await log_unlock_event_to_timeline(
                device_id=client_id,
                unlock_type=command,
                mode=mode
            )

    except Exception as e:
        logger.warning(f"Failed to log command to timeline: {e}")

    logger.info(f"Command '{command}' response: {response}")
    return response


def _determine_mode_from_command(command: str) -> str:
    """Determine operational mode from command type."""
    command_mode_mapping = {
        "unlock_service": "service",
        "start": "manager",
        "stop": "manager",
        "status": "manager"
    }
    return command_mode_mapping.get(command, "mqtt")


async def handle_unlock_command(client_id: str, payload: str) -> Dict[str, Any]:
    """Handle unlock command."""
    logger.info(f"Processing unlock command for client {client_id}")
    
    # Simulate unlock operation
    await asyncio.sleep(1)
    
    return {
        "message": "Device unlocked successfully",
        "operation": "unlock",
        "duration_ms": 1000
    }


async def handle_unlock_service_command(client_id: str, payload: str) -> Dict[str, Any]:
    """Handle unlock service command."""
    logger.info(f"Processing unlock service command for client {client_id}")
    
    # Simulate service unlock operation
    await asyncio.sleep(1)
    
    return {
        "message": "Service unlocked successfully",
        "operation": "unlock_service",
        "duration_ms": 1000
    }


async def handle_start_command(client_id: str, payload: str) -> Dict[str, Any]:
    """Handle start command."""
    logger.info(f"Processing start command for client {client_id}")
    
    return {
        "message": "Service started successfully",
        "operation": "start",
        "status": "running"
    }


async def handle_stop_command(client_id: str, payload: str) -> Dict[str, Any]:
    """Handle stop command."""
    logger.info(f"Processing stop command for client {client_id}")
    
    return {
        "message": "Service stopped successfully",
        "operation": "stop",
        "status": "stopped"
    }


async def handle_status_command(client_id: str, payload: str) -> Dict[str, Any]:
    """Handle status command."""
    logger.info(f"Processing status command for client {client_id}")
    
    # Get current device status
    from config import device_config
    
    return {
        "message": "Status retrieved successfully",
        "operation": "status",
        "device_status": {
            "device_type": device_config.device_type,
            "enabled_features": device_config.enabled_features,
            "mqtt_connected": get_mqtt_client() is not None and get_mqtt_client().is_connected,
            "uptime": "unknown"  # Could be enhanced to track actual uptime
        }
    }


async def handle_ping_command(client_id: str, payload: str) -> Dict[str, Any]:
    """Handle ping command."""
    logger.info(f"Processing ping command for client {client_id}")
    
    return {
        "message": "Pong",
        "operation": "ping",
        "echo": payload if payload else None
    }


def register_mqtt_handlers():
    """Register MQTT message handlers with the client."""
    mqtt_client = get_mqtt_client()
    if not mqtt_client:
        logger.warning("MQTT client not available for handler registration")
        return
        
    # Register handler for device commands
    mqtt_client.register_message_handler(
        "devices/+/commands/electronic/#",
        handle_device_command
    )
    
    logger.info("MQTT message handlers registered successfully")


# Enhanced command handlers that could integrate with existing systems
async def handle_hardware_command(client_id: str, command: str, payload: str) -> Dict[str, Any]:
    """
    Handle hardware-specific commands that integrate with the FSM system.
    This is an example of how to integrate MQTT commands with existing hardware control.
    """
    from config import device_config
    
    if not device_config.enabled_features.get("fsm"):
        return {
            "success": False,
            "error": "FSM feature not enabled",
            "command": command
        }
    
    try:
        # Example integration with hardware control
        # This would need to be adapted based on your specific hardware interface
        
        if command == "open_door":
            # Example: Open a specific door
            # from hardware.locker_control import open_door
            # result = await open_door(door_id)
            return {
                "message": f"Door command executed",
                "operation": command,
                "status": "completed"
            }
            
        elif command == "check_sensors":
            # Example: Check sensor status
            return {
                "message": "Sensor status checked",
                "operation": command,
                "sensors": {
                    "door_open": False,
                    "temperature": 22.5,
                    "humidity": 45
                }
            }
            
        else:
            return {
                "success": False,
                "error": f"Unknown hardware command: {command}",
                "available_commands": ["open_door", "check_sensors"]
            }
            
    except Exception as e:
        logger.error(f"Error executing hardware command '{command}': {e}")
        return {
            "success": False,
            "error": str(e),
            "command": command
        }


async def handle_storage_command(client_id: str, command: str, payload: str) -> Dict[str, Any]:
    """
    Handle storage-specific commands.
    Example of how to integrate MQTT with storage functionality.
    """
    from config import device_config
    
    if not device_config.enabled_features.get("storage"):
        return {
            "success": False,
            "error": "Storage feature not enabled",
            "command": command
        }
    
    try:
        if command == "get_storage_status":
            # Example: Get current storage status
            return {
                "message": "Storage status retrieved",
                "operation": command,
                "storage": {
                    "available_slots": 10,
                    "occupied_slots": 5,
                    "total_capacity": 15
                }
            }
            
        elif command == "reserve_slot":
            # Example: Reserve a storage slot
            return {
                "message": "Storage slot reserved",
                "operation": command,
                "slot_id": "A1",
                "reservation_id": "12345"
            }
            
        else:
            return {
                "success": False,
                "error": f"Unknown storage command: {command}",
                "available_commands": ["get_storage_status", "reserve_slot"]
            }
            
    except Exception as e:
        logger.error(f"Error executing storage command '{command}': {e}")
        return {
            "success": False,
            "error": str(e),
            "command": command
        }
