import asyncio
import json
import logging
from typing import Dict, Any
from mqtt.client import get_mqtt_client

logger = logging.getLogger(__name__)


async def handle_device_command(topic: str, payload: str, msg) -> Dict[str, Any]:
    """
    Handle device commands received via MQTT.

    Expected topic format: devices/{client_id}/electronic/{command}
    """
    logger.info(f"Handling device command from topic: {topic}")

    try:
        # Parse topic to extract client_id and command
        parts = topic.split("/")
        if len(parts) < 4 or parts[2] != "electronic":
            logger.warning(f"Invalid command topic format: {topic}")
            return {
                "success": False,
                "error": "Invalid topic format",
                "topic": topic
            }

        client_id = parts[1]
        command = parts[3]

        if not command:
            logger.warning(f"No command found in topic: {topic}")
            return {
                "success": False,
                "error": "No command specified",
                "topic": topic
            }

        logger.info(f"Processing command '{command}' for client '{client_id}'")

        # Parse payload as JSON
        try:
            payload_data = json.loads(payload) if payload else {}
        except json.JSONDecodeError:
            logger.warning(f"Invalid JSON payload: {payload}")
            payload_data = {}

        # Execute the command
        response = await execute_command(client_id, command, payload_data)

        # Publish response back via MQTT
        mqtt_client = get_mqtt_client()
        if mqtt_client:
            await mqtt_client.publish_response(client_id, command, response)
        else:
            logger.warning("MQTT client not available for response publishing")

        return response

    except Exception as e:
        logger.error(f"Error handling device command: {e}")
        return {
            "success": False,
            "error": str(e),
            "topic": topic
        }


async def execute_command(client_id: str, command: str, payload_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Execute a specific command and return the response.

    Args:
        client_id: The client ID from the MQTT topic
        command: The command to execute
        payload_data: Payload data as dictionary

    Returns:
        Dict containing the command response
    """
    logger.info(f"Executing command '{command}' for client '{client_id}'")

    # Base response structure
    response = {
        "success": True,
        "client_id": client_id,
        "command": command,
        "timestamp": asyncio.get_event_loop().time()
    }

    try:
        if command == "section_open":
            response.update(await handle_section_open(client_id, payload_data))
        else:
            logger.warning(f"Unknown command: {command}")
            response.update({
                "success": False,
                "error": f"Unknown command: {command}",
                "available_commands": ["section_open"]
            })

    except Exception as e:
        logger.error(f"Error executing command '{command}': {e}")
        response.update({
            "success": False,
            "error": str(e)
        })

    # Log command execution to timeline
    try:
        from mqtt.timeline_integration import log_mqtt_command_to_timeline

        # Log the command execution
        await log_mqtt_command_to_timeline(
            client_id=client_id,
            command=command,
            payload=json.dumps(payload_data),
            response=response,
            mode="electronic"
        )

    except Exception as e:
        logger.warning(f"Failed to log command to timeline: {e}")

    logger.info(f"Command '{command}' response: {response}")
    return response


async def handle_section_open(client_id: str, payload_data: Dict[str, Any]) -> Dict[str, Any]:
    """Handle section_open command."""
    section_id = payload_data.get("section_id")

    if section_id is None:
        return {
            "success": False,
            "error": "section_id is required"
        }

    if not isinstance(section_id, int):
        return {
            "success": False,
            "error": "section_id must be an integer"
        }

    logger.info(f"Opening section {section_id} for client {client_id}")

    # TODO: Implement actual electronic communication to open section
    # For now, simulate the operation
    await asyncio.sleep(0.5)

    return {
        "success": True,
        "section_id": section_id,
        "message": f"Section {section_id} opened successfully"
    }


def register_mqtt_handlers():
    """Register MQTT message handlers with the client."""
    mqtt_client = get_mqtt_client()
    if not mqtt_client:
        logger.warning("MQTT client not available for handler registration")
        return

    # Register handler for electronic commands
    mqtt_client.register_message_handler(
        "devices/+/electronic/#",
        handle_device_command
    )

    logger.info("MQTT message handlers registered successfully")
