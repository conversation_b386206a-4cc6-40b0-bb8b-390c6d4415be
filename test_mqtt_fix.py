#!/usr/bin/env python3
"""
Quick test to verify MQTT functionality after fixes
"""

import asyncio
import json
import logging
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_mqtt_with_debug():
    """Test MQTT with debug logging."""
    try:
        from mqtt.client import AsyncMQTTClient
        from mqtt.handlers import handle_device_command
        from config import device_config
        
        logger.info("Creating MQTT client...")
        client = AsyncMQTTClient()
        
        # Set main loop
        main_loop = asyncio.get_running_loop()
        client.set_main_loop(main_loop)
        
        # Register handler
        logger.info("Registering message handler...")
        client.register_message_handler(
            "devices/+/commands/electronic/#",
            handle_device_command
        )
        
        logger.info(f"Registered handlers: {list(client.message_handlers.keys())}")
        
        # Start client
        logger.info("Starting MQTT client...")
        await client.start()
        
        if client.is_connected and client.is_subscribed:
            logger.info("✓ MQTT client is ready and listening")
            logger.info("Send a command to test...")
            logger.info(f"Example: devices/{device_config.mqtt_config['client_id']}/commands/electronic/unlock")
            
            # Keep running for 30 seconds
            for i in range(30):
                await asyncio.sleep(1)
                if i % 10 == 0:
                    logger.info(f"Still listening... ({30-i} seconds remaining)")
                    
        else:
            logger.error("✗ MQTT client failed to initialize properly")
            
        await client.stop()
        logger.info("MQTT client stopped")
        
    except Exception as e:
        logger.error(f"Error in test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        asyncio.run(test_mqtt_with_debug())
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test failed: {e}")
        sys.exit(1)
