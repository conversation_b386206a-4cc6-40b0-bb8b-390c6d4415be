"""
MQTT Timeline Integration

This module provides integration between MQTT events and the timeline logging system.
It automatically logs MQTT commands and events to the timeline_log table for audit purposes.
"""

import logging
from typing import Dict, Any, Optional
from managers.timeline_logger import log_timeline_event
from config import device_config

logger = logging.getLogger(__name__)


async def log_mqtt_command_to_timeline(
    client_id: str,
    command: str,
    payload: str,
    response: Dict[str, Any],
    session_id: Optional[str] = None,
    mode: Optional[str] = None
) -> Optional[int]:
    """
    Log an MQTT command to the timeline for audit purposes.
    
    Args:
        client_id: The MQTT client ID
        command: The command that was executed
        payload: The original command payload
        response: The command response
        session_id: Optional session ID if available
        mode: Optional operational mode (storage/product/hygiene/manager)
        
    Returns:
        Timeline log ID if successful, None otherwise
    """
    try:
        # Determine event result based on response
        event_result = "success" if response.get("success", True) else "failed"
        
        # Create a descriptive message
        if response.get("success", True):
            message = response.get("message", f"MQTT command '{command}' executed successfully")
        else:
            error = response.get("error", "Unknown error")
            message = f"MQTT command '{command}' failed: {error}"
        
        # Log to timeline
        timeline_id = log_timeline_event(
            serial_number=device_config.device_settings.get("serial_number") or client_id,
            event_type="mqtt_command",
            event_result=event_result,
            message=message,
            mode=mode,
            session_id=session_id
        )
        
        if timeline_id:
            logger.info(f"MQTT command logged to timeline: {timeline_id}")
        else:
            logger.warning(f"Failed to log MQTT command to timeline")
            
        return timeline_id
        
    except Exception as e:
        logger.error(f"Error logging MQTT command to timeline: {e}")
        return None


async def log_mqtt_event_to_timeline(
    event_type: str,
    event_data: Dict[str, Any],
    device_id: str,
    session_id: Optional[str] = None,
    mode: Optional[str] = None
) -> Optional[int]:
    """
    Log an MQTT event to the timeline.
    
    Args:
        event_type: The type of event (e.g., 'door_state', 'transaction', 'storage')
        event_data: The event data
        device_id: The device identifier
        session_id: Optional session ID if available
        mode: Optional operational mode
        
    Returns:
        Timeline log ID if successful, None otherwise
    """
    try:
        # Map MQTT event types to timeline event types
        timeline_event_type = _map_mqtt_event_to_timeline_type(event_type, event_data)
        
        # Determine event result
        event_result = event_data.get("status", "completed")
        if event_data.get("error"):
            event_result = "failed"
        
        # Create message from event data
        message = _create_event_message(event_type, event_data)
        
        # Extract additional fields from event data
        section_id = event_data.get("door_id") or event_data.get("slot_id")
        
        # Log to timeline
        timeline_id = log_timeline_event(
            serial_number=device_config.device_settings.get("serial_number") or device_id,
            event_type=timeline_event_type,
            event_result=event_result,
            section_id=section_id,
            message=message,
            mode=mode,
            session_id=session_id
        )
        
        if timeline_id:
            logger.info(f"MQTT event logged to timeline: {timeline_id}")
        else:
            logger.warning(f"Failed to log MQTT event to timeline")
            
        return timeline_id
        
    except Exception as e:
        logger.error(f"Error logging MQTT event to timeline: {e}")
        return None


def _map_mqtt_event_to_timeline_type(event_type: str, event_data: Dict[str, Any]) -> str:
    """
    Map MQTT event types to timeline event types.
    
    Args:
        event_type: The MQTT event type
        event_data: The event data
        
    Returns:
        Appropriate timeline event type
    """
    # Map common MQTT event types to timeline types
    event_mapping = {
        "door_state": "door_state",
        "transaction": "transaction",
        "storage": "storage_operation",
        "error": "error",
        "status": "status_update",
        "payment_completed": "payment",
        "unlock": "unlock",
        "lock": "lock"
    }
    
    # Check for specific actions within event data
    if event_type == "door_state":
        action = event_data.get("action", "")
        if action in ["opened", "closed"]:
            return "door_state"
        elif action in ["locked", "unlocked"]:
            return action
    
    return event_mapping.get(event_type, "mqtt_event")


def _create_event_message(event_type: str, event_data: Dict[str, Any]) -> str:
    """
    Create a descriptive message for the timeline log.
    
    Args:
        event_type: The MQTT event type
        event_data: The event data
        
    Returns:
        Descriptive message string
    """
    try:
        if event_type == "door_state":
            door_id = event_data.get("door_id", "unknown")
            action = event_data.get("action", "unknown")
            return f"Door {door_id} {action} via MQTT"
            
        elif event_type == "transaction":
            transaction_id = event_data.get("transaction_id", "unknown")
            status = event_data.get("status", "unknown")
            amount = event_data.get("amount")
            if amount:
                return f"Transaction {transaction_id} {status} (amount: {amount}) via MQTT"
            else:
                return f"Transaction {transaction_id} {status} via MQTT"
                
        elif event_type == "storage":
            slot_id = event_data.get("slot_id", "unknown")
            action = event_data.get("action", "unknown")
            return f"Storage slot {slot_id} {action} via MQTT"
            
        elif event_type == "error":
            error_type = event_data.get("error_type", "unknown")
            error_message = event_data.get("error_message", "")
            return f"MQTT Error - {error_type}: {error_message}"
            
        else:
            # Generic message for other event types
            return f"MQTT event: {event_type}"
            
    except Exception as e:
        logger.warning(f"Error creating event message: {e}")
        return f"MQTT event: {event_type}"


async def log_door_event_to_timeline(
    door_id: str,
    action: str,
    device_id: str,
    session_id: Optional[str] = None,
    mode: Optional[str] = None,
    **kwargs
) -> Optional[int]:
    """
    Convenience function to log door events to timeline.
    
    Args:
        door_id: The door identifier
        action: The action performed (opened, closed, locked, unlocked)
        device_id: The device identifier
        session_id: Optional session ID
        mode: Optional operational mode
        **kwargs: Additional event data
        
    Returns:
        Timeline log ID if successful, None otherwise
    """
    event_data = {
        "door_id": door_id,
        "action": action,
        **kwargs
    }
    
    return await log_mqtt_event_to_timeline(
        "door_state",
        event_data,
        device_id,
        session_id,
        mode
    )


async def log_unlock_event_to_timeline(
    device_id: str,
    unlock_type: str = "unlock",
    session_id: Optional[str] = None,
    mode: Optional[str] = None,
    **kwargs
) -> Optional[int]:
    """
    Convenience function to log unlock events to timeline.
    
    Args:
        device_id: The device identifier
        unlock_type: Type of unlock (unlock, unlock_service)
        session_id: Optional session ID
        mode: Optional operational mode
        **kwargs: Additional event data
        
    Returns:
        Timeline log ID if successful, None otherwise
    """
    # Determine if this is a tempered unlock based on mode or unlock type
    tempered_unlock = 1 if unlock_type == "unlock_service" or mode == "service" else 0
    
    try:
        timeline_id = log_timeline_event(
            serial_number=device_config.device_settings.get("serial_number") or device_id,
            event_type="unlock",
            event_result="success",
            tempered_unlock=tempered_unlock,
            message=f"Device unlocked via MQTT ({unlock_type})",
            mode=mode,
            session_id=session_id
        )
        
        if timeline_id:
            logger.info(f"Unlock event logged to timeline: {timeline_id}")
        else:
            logger.warning("Failed to log unlock event to timeline")
            
        return timeline_id
        
    except Exception as e:
        logger.error(f"Error logging unlock event to timeline: {e}")
        return None


# Integration with existing MQTT handlers
def enhance_mqtt_handlers_with_timeline_logging():
    """
    Enhance existing MQTT handlers to include timeline logging.
    This function modifies the handlers to automatically log events.
    """
    try:
        from mqtt.handlers import execute_command
        
        # Store original execute_command function
        original_execute_command = execute_command
        
        async def enhanced_execute_command(client_id: str, command: str, payload: str = "") -> Dict[str, Any]:
            """Enhanced execute_command with timeline logging."""
            # Execute original command
            response = await original_execute_command(client_id, command, payload)
            
            # Log to timeline
            await log_mqtt_command_to_timeline(
                client_id=client_id,
                command=command,
                payload=payload,
                response=response,
                mode=_determine_mode_from_command(command)
            )
            
            # Log specific events based on command type
            if command in ["unlock", "unlock_service"] and response.get("success", True):
                await log_unlock_event_to_timeline(
                    device_id=client_id,
                    unlock_type=command,
                    mode=_determine_mode_from_command(command)
                )
            
            return response
        
        # Replace the function in the handlers module
        import mqtt.handlers
        mqtt.handlers.execute_command = enhanced_execute_command
        
        logger.info("MQTT handlers enhanced with timeline logging")
        
    except Exception as e:
        logger.error(f"Error enhancing MQTT handlers with timeline logging: {e}")


def _determine_mode_from_command(command: str) -> Optional[str]:
    """
    Determine operational mode from command type.
    
    Args:
        command: The MQTT command
        
    Returns:
        Operational mode string or None
    """
    # Map commands to operational modes
    command_mode_mapping = {
        "unlock_service": "service",
        "start": "manager",
        "stop": "manager",
        "status": "manager"
    }
    
    return command_mode_mapping.get(command)
