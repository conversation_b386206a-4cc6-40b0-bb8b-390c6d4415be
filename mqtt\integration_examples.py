"""
MQTT Integration Examples

This file contains examples of how to integrate MQTT functionality
with existing systems in the Waffles Backend application.
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from config import device_config
from mqtt import (
    mqtt_service,
    publish_door_event,
    publish_transaction_event,
    publish_storage_event,
    publish_error_event
)

logger = logging.getLogger(__name__)


class MQTTIntegrationExamples:
    """Examples of MQTT integration with existing systems."""
    
    @staticmethod
    async def integrate_with_hardware_events():
        """Example: Integrate MQTT with hardware events."""
        
        # Example: When a door is opened by hardware
        async def on_door_opened(door_id: str, session_id: Optional[str] = None):
            """Called when hardware detects door opening."""
            device_id = device_config.mqtt_config.get("client_id", "unknown")
            
            # Publish door event via MQTT
            success = await publish_door_event(
                device_id=device_id,
                door_id=door_id,
                action="opened",
                session_id=session_id,
                timestamp=asyncio.get_event_loop().time()
            )
            
            if success:
                logger.info(f"Door {door_id} opened event published via MQTT")
            else:
                logger.warning(f"Failed to publish door {door_id} opened event")
        
        # Example: When a door is closed by hardware
        async def on_door_closed(door_id: str, session_id: Optional[str] = None):
            """Called when hardware detects door closing."""
            device_id = device_config.mqtt_config.get("client_id", "unknown")
            
            # Publish door event via MQTT
            success = await publish_door_event(
                device_id=device_id,
                door_id=door_id,
                action="closed",
                session_id=session_id,
                timestamp=asyncio.get_event_loop().time()
            )
            
            if success:
                logger.info(f"Door {door_id} closed event published via MQTT")
            else:
                logger.warning(f"Failed to publish door {door_id} closed event")
        
        return on_door_opened, on_door_closed
    
    @staticmethod
    async def integrate_with_transaction_system():
        """Example: Integrate MQTT with transaction system."""
        
        async def on_transaction_started(transaction_id: str, amount: float, transaction_type: str):
            """Called when a transaction is started."""
            device_id = device_config.mqtt_config.get("client_id", "unknown")
            
            success = await publish_transaction_event(
                device_id=device_id,
                transaction_id=transaction_id,
                status="started",
                amount=amount,
                transaction_type=transaction_type
            )
            
            if success:
                logger.info(f"Transaction {transaction_id} started event published via MQTT")
        
        async def on_transaction_completed(transaction_id: str, amount: float, success: bool):
            """Called when a transaction is completed."""
            device_id = device_config.mqtt_config.get("client_id", "unknown")
            
            status = "completed" if success else "failed"
            
            success_published = await publish_transaction_event(
                device_id=device_id,
                transaction_id=transaction_id,
                status=status,
                amount=amount
            )
            
            if success_published:
                logger.info(f"Transaction {transaction_id} {status} event published via MQTT")
        
        return on_transaction_started, on_transaction_completed
    
    @staticmethod
    async def integrate_with_storage_system():
        """Example: Integrate MQTT with storage system."""
        
        async def on_storage_reserved(slot_id: str, reservation_id: str, user_id: Optional[str] = None):
            """Called when a storage slot is reserved."""
            device_id = device_config.mqtt_config.get("client_id", "unknown")
            
            success = await publish_storage_event(
                device_id=device_id,
                slot_id=slot_id,
                action="reserved",
                reservation_id=reservation_id,
                user_id=user_id
            )
            
            if success:
                logger.info(f"Storage slot {slot_id} reserved event published via MQTT")
        
        async def on_storage_occupied(slot_id: str, reservation_id: str):
            """Called when a storage slot is occupied."""
            device_id = device_config.mqtt_config.get("client_id", "unknown")
            
            success = await publish_storage_event(
                device_id=device_id,
                slot_id=slot_id,
                action="occupied",
                reservation_id=reservation_id
            )
            
            if success:
                logger.info(f"Storage slot {slot_id} occupied event published via MQTT")
        
        async def on_storage_released(slot_id: str, reservation_id: str):
            """Called when a storage slot is released."""
            device_id = device_config.mqtt_config.get("client_id", "unknown")
            
            success = await publish_storage_event(
                device_id=device_id,
                slot_id=slot_id,
                action="released",
                reservation_id=reservation_id
            )
            
            if success:
                logger.info(f"Storage slot {slot_id} released event published via MQTT")
        
        return on_storage_reserved, on_storage_occupied, on_storage_released
    
    @staticmethod
    async def integrate_with_error_handling():
        """Example: Integrate MQTT with error handling system."""
        
        async def on_hardware_error(error_type: str, error_message: str, severity: str = "error"):
            """Called when a hardware error occurs."""
            device_id = device_config.mqtt_config.get("client_id", "unknown")
            
            success = await publish_error_event(
                device_id=device_id,
                error_type=error_type,
                error_message=error_message,
                severity=severity,
                component="hardware"
            )
            
            if success:
                logger.info(f"Hardware error event published via MQTT: {error_type}")
        
        async def on_communication_error(error_message: str, endpoint: str):
            """Called when a communication error occurs."""
            device_id = device_config.mqtt_config.get("client_id", "unknown")
            
            success = await publish_error_event(
                device_id=device_id,
                error_type="communication_error",
                error_message=error_message,
                severity="warning",
                endpoint=endpoint
            )
            
            if success:
                logger.info(f"Communication error event published via MQTT")
        
        return on_hardware_error, on_communication_error
    
    @staticmethod
    async def publish_device_status_periodically():
        """Example: Publish device status periodically."""
        
        async def status_publisher():
            """Periodically publish device status."""
            device_id = device_config.mqtt_config.get("client_id", "unknown")
            
            while True:
                try:
                    # Collect device status information
                    status = {
                        "online": True,
                        "device_type": device_config.device_type,
                        "enabled_features": device_config.enabled_features,
                        "mqtt_connected": mqtt_service.is_connected(),
                        "uptime": asyncio.get_event_loop().time(),  # Simple uptime
                        "last_update": asyncio.get_event_loop().time()
                    }
                    
                    # Add feature-specific status
                    if device_config.enabled_features.get("fsm"):
                        # Add FSM status if available
                        status["fsm_status"] = {
                            "hardware_connected": True,  # This would be actual hardware status
                            "doors_locked": True,  # This would be actual door status
                        }
                    
                    if device_config.enabled_features.get("storage"):
                        # Add storage status if available
                        status["storage_status"] = {
                            "available_slots": 10,  # This would be actual slot count
                            "occupied_slots": 3,    # This would be actual occupied count
                        }
                    
                    # Publish status
                    success = await mqtt_service.publish_device_status(device_id, status)
                    
                    if success:
                        logger.debug("Device status published via MQTT")
                    else:
                        logger.warning("Failed to publish device status via MQTT")
                    
                    # Wait 60 seconds before next status update
                    await asyncio.sleep(60)
                    
                except Exception as e:
                    logger.error(f"Error in status publisher: {e}")
                    await asyncio.sleep(60)  # Wait before retrying
        
        return status_publisher
    
    @staticmethod
    async def handle_remote_commands():
        """Example: Handle remote commands received via MQTT."""
        
        # This would typically be integrated into the existing command handlers
        # but here's an example of how to extend functionality
        
        async def handle_custom_remote_command(command: str, payload: Dict[str, Any]) -> Dict[str, Any]:
            """Handle custom remote commands."""
            
            if command == "get_device_info":
                return {
                    "device_type": device_config.device_type,
                    "serial_number": device_config.device_settings.get("serial_number", "unknown"),
                    "enabled_features": device_config.enabled_features,
                    "mqtt_config": {
                        "host": device_config.mqtt_config.get("host"),
                        "port": device_config.mqtt_config.get("port"),
                        "client_id": device_config.mqtt_config.get("client_id")
                    }
                }
            
            elif command == "restart_service":
                service_name = payload.get("service", "unknown")
                # Here you would implement actual service restart logic
                logger.info(f"Restart requested for service: {service_name}")
                return {
                    "message": f"Service {service_name} restart initiated",
                    "status": "pending"
                }
            
            elif command == "update_config":
                config_key = payload.get("key")
                config_value = payload.get("value")
                # Here you would implement configuration update logic
                logger.info(f"Config update requested: {config_key} = {config_value}")
                return {
                    "message": f"Configuration update for {config_key} initiated",
                    "status": "pending"
                }
            
            else:
                return {
                    "success": False,
                    "error": f"Unknown remote command: {command}",
                    "available_commands": ["get_device_info", "restart_service", "update_config"]
                }
        
        return handle_custom_remote_command


# Example usage functions
async def setup_mqtt_integrations():
    """Setup all MQTT integrations."""
    logger.info("Setting up MQTT integrations...")
    
    examples = MQTTIntegrationExamples()
    
    # Setup hardware event handlers
    on_door_opened, on_door_closed = await examples.integrate_with_hardware_events()
    
    # Setup transaction event handlers
    on_transaction_started, on_transaction_completed = await examples.integrate_with_transaction_system()
    
    # Setup storage event handlers
    on_storage_reserved, on_storage_occupied, on_storage_released = await examples.integrate_with_storage_system()
    
    # Setup error handlers
    on_hardware_error, on_communication_error = await examples.integrate_with_error_handling()
    
    # Start periodic status publisher
    status_publisher = await examples.publish_device_status_periodically()
    asyncio.create_task(status_publisher())
    
    logger.info("MQTT integrations setup complete")
    
    return {
        "door_handlers": (on_door_opened, on_door_closed),
        "transaction_handlers": (on_transaction_started, on_transaction_completed),
        "storage_handlers": (on_storage_reserved, on_storage_occupied, on_storage_released),
        "error_handlers": (on_hardware_error, on_communication_error)
    }
