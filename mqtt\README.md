# MQTT Module

This module provides MQTT client functionality for the Waffles Backend application. It enables the device to receive commands via MQTT and publish responses, events, and status updates.

## Features

- **Async MQTT Client**: Non-blocking MQTT communication using paho-mqtt
- **Command Handling**: Process device commands received via MQTT
- **Response Publishing**: Send command responses back via MQTT
- **Event Publishing**: Publish device events (door states, transactions, etc.)
- **Status Publishing**: Publish device status and health information
- **Configuration Integration**: Uses settings from `.env` file and `config.py`
- **Error Handling**: Robust error handling and logging
- **Reconnection**: Automatic reconnection handling (via paho-mqtt)

## Configuration

MQTT configuration is handled through environment variables in the `.env` file:

```env
# MQTT Configuration
MQTT_HOST=*************
MQTT_PORT=1883
MQTT_USERNAME=1234
MQTT_PASSWORD=1234
MQTT_CLIENT_ID=1234
MQTT_ENABLED=true
MQTT_CONNECTION_TIMEOUT=15
MQTT_KEEPALIVE=60
```

## Topic Structure

The MQTT implementation uses a structured topic hierarchy:

### Command Topics (Subscribed)
- `devices/{client_id}/commands/electronic/{command}` - Device commands

### Response Topics (Published)
- `devices/{client_id}/responses/electronic/{command}` - Command responses

### Event Topics (Published)
- `devices/{client_id}/events/{event_type}` - Device events
- `devices/{client_id}/status` - Device status (retained)
- `devices/{client_id}/notifications` - Device notifications

## Supported Commands

The following commands are supported by default:

- `unlock` - Unlock the device
- `unlock_service` - Unlock service mode
- `start` - Start the service
- `stop` - Stop the service
- `status` - Get device status
- `ping` - Ping/pong test command

## Usage

### Basic Integration

The MQTT client is automatically initialized when the FastAPI application starts:

```python
# In main.py - already integrated
from mqtt import initialize_mqtt_client, register_mqtt_handlers

mqtt_client = await initialize_mqtt_client()
if mqtt_client:
    register_mqtt_handlers()
```

### Using the MQTT Service

```python
from mqtt import mqtt_service, publish_door_event

# Check connection status
if mqtt_service.is_connected():
    print("MQTT is connected")

# Publish a door event
await publish_door_event("1234", "door_1", "opened", user_id="user123")

# Publish device status
status = {
    "online": True,
    "temperature": 22.5,
    "doors_locked": True
}
await mqtt_service.publish_device_status("1234", status)

# Publish a custom event
event_data = {"transaction_id": "tx123", "amount": 50.0}
await mqtt_service.publish_event("1234", "payment_completed", event_data)
```

### Custom Command Handlers

You can extend the command handling by modifying `mqtt/handlers.py`:

```python
async def handle_custom_command(client_id: str, payload: str) -> Dict[str, Any]:
    """Handle a custom command."""
    # Your custom logic here
    return {
        "message": "Custom command executed",
        "operation": "custom_command",
        "result": "success"
    }

# Add to execute_command function
elif command == "custom_command":
    response.update(await handle_custom_command(client_id, payload))
```

## Testing

Use the provided test script to verify MQTT functionality:

```bash
# Test basic connection
python mqtt/test_mqtt.py connection

# Test with message handlers (interactive)
python mqtt/test_mqtt.py handlers

# Send a test command
python mqtt/test_mqtt.py send
```

## Integration Examples

### Hardware Integration

```python
from mqtt import publish_door_event

async def on_door_opened(door_id: str):
    """Called when a door is opened."""
    device_id = device_config.mqtt_config["client_id"]
    await publish_door_event(device_id, door_id, "opened")
```

### Transaction Integration

```python
from mqtt import publish_transaction_event

async def on_transaction_completed(transaction_id: str, amount: float):
    """Called when a transaction is completed."""
    device_id = device_config.mqtt_config["client_id"]
    await publish_transaction_event(
        device_id, 
        transaction_id, 
        "completed",
        amount=amount
    )
```

### Error Reporting

```python
from mqtt import publish_error_event

async def report_hardware_error(error_message: str):
    """Report a hardware error via MQTT."""
    device_id = device_config.mqtt_config["client_id"]
    await publish_error_event(
        device_id,
        "hardware_error",
        error_message,
        severity="critical"
    )
```

## Architecture

```
mqtt/
├── __init__.py          # Module exports
├── client.py            # AsyncMQTTClient implementation
├── handlers.py          # Command handlers
├── service.py           # High-level service functions
├── test_mqtt.py         # Test script
└── README.md           # This file
```

### Key Components

1. **AsyncMQTTClient** (`client.py`): Core MQTT client with async support
2. **Command Handlers** (`handlers.py`): Process incoming MQTT commands
3. **MQTT Service** (`service.py`): High-level API for publishing events/status
4. **Configuration**: Integrated with main application config system

## Logging

The MQTT module uses Python's standard logging system. Set the log level in your environment:

```env
LOG_LEVEL=INFO  # or DEBUG for more verbose output
```

MQTT-specific logs are prefixed with the module name (e.g., `mqtt.client`, `mqtt.handlers`).

## Error Handling

The module includes comprehensive error handling:

- Connection failures are logged and can trigger reconnection
- Message processing errors are caught and logged
- Publishing failures are reported but don't crash the application
- Invalid commands return structured error responses

## Security Considerations

- Always use authentication (username/password) in production
- Consider using TLS/SSL for encrypted communication
- Validate all incoming command payloads
- Implement proper access control on the MQTT broker
- Use appropriate QoS levels for different message types

## Performance

- The client uses QoS 1 for command responses (guaranteed delivery)
- Status messages use retain flag for latest state persistence
- Event messages use QoS 1 without retain
- Connection keepalive is configurable (default 60 seconds)

## Troubleshooting

### Common Issues

1. **Connection Failed**: Check host, port, username, and password
2. **Subscription Failed**: Verify topic permissions on MQTT broker
3. **Messages Not Received**: Check topic patterns and QoS settings
4. **Publishing Failed**: Verify write permissions and topic structure

### Debug Mode

Enable debug logging for detailed MQTT communication:

```env
LOG_LEVEL=DEBUG
```

This will show all MQTT messages, connection events, and internal state changes.
