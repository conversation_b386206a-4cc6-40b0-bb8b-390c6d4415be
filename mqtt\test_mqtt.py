#!/usr/bin/env python3
"""
MQTT Test Script

This script can be used to test the MQTT functionality independently
or as part of the application testing suite.
"""

import asyncio
import json
import logging
import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import from the main application
sys.path.insert(0, str(Path(__file__).parent.parent))

from dotenv import load_dotenv
from mqtt.client import AsyncMQTTClient
from mqtt.handlers import register_mqtt_handlers
from config import device_config

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_mqtt_connection():
    """Test basic MQTT connection."""
    logger.info("Testing MQTT connection...")
    
    try:
        # Create MQTT client
        client = AsyncMQTTClient()
        
        # Set main loop
        main_loop = asyncio.get_running_loop()
        client.set_main_loop(main_loop)
        
        # Start client
        await client.start()
        
        if client.is_connected:
            logger.info("✓ MQTT connection successful")
            
            # Test publishing a status message
            test_response = {
                "test": True,
                "message": "MQTT test successful",
                "timestamp": asyncio.get_event_loop().time()
            }
            
            success = await client.publish_response(
                device_config.mqtt_config["client_id"],
                "test",
                test_response
            )
            
            if success:
                logger.info("✓ MQTT publishing successful")
            else:
                logger.error("✗ MQTT publishing failed")
                
            # Keep running for a few seconds to receive any messages
            logger.info("Listening for messages for 10 seconds...")
            await asyncio.sleep(10)
            
        else:
            logger.error("✗ MQTT connection failed")
            
        # Stop client
        await client.stop()
        logger.info("MQTT client stopped")
        
    except Exception as e:
        logger.error(f"Error testing MQTT: {e}")
        raise


async def test_mqtt_with_handlers():
    """Test MQTT with message handlers."""
    logger.info("Testing MQTT with handlers...")
    
    try:
        # Create MQTT client
        client = AsyncMQTTClient()
        
        # Set main loop
        main_loop = asyncio.get_running_loop()
        client.set_main_loop(main_loop)
        
        # Register handlers
        from mqtt.handlers import handle_device_command
        client.register_message_handler(
            "devices/+/commands/electronic/#",
            handle_device_command
        )
        
        # Start client
        await client.start()
        
        if client.is_connected and client.is_subscribed:
            logger.info("✓ MQTT connection and subscription successful")
            
            # Keep running to listen for commands
            logger.info("Listening for commands. Send a command to test...")
            logger.info(f"Example topic: devices/{device_config.mqtt_config['client_id']}/commands/electronic/ping")
            logger.info("Press Ctrl+C to stop")
            
            try:
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                logger.info("Stopping due to keyboard interrupt...")
                
        else:
            logger.error("✗ MQTT connection or subscription failed")
            
        # Stop client
        await client.stop()
        logger.info("MQTT client stopped")
        
    except Exception as e:
        logger.error(f"Error testing MQTT with handlers: {e}")
        raise


async def send_test_command():
    """Send a test command to the MQTT broker."""
    logger.info("Sending test command...")
    
    try:
        import paho.mqtt.client as mqtt
        
        # Create a simple publisher client
        publisher = mqtt.Client(client_id="test_publisher")
        
        # Set authentication
        if device_config.mqtt_config["username"] and device_config.mqtt_config["password"]:
            publisher.username_pw_set(
                device_config.mqtt_config["username"],
                device_config.mqtt_config["password"]
            )
        
        # Connect and publish
        publisher.connect(
            device_config.mqtt_config["host"],
            device_config.mqtt_config["port"],
            60
        )
        
        # Send a ping command
        topic = f"devices/{device_config.mqtt_config['client_id']}/commands/electronic/ping"
        payload = json.dumps({"test": True, "message": "Test ping command"})
        
        result = publisher.publish(topic, payload, qos=1)
        result.wait_for_publish()
        
        logger.info(f"✓ Test command sent to topic: {topic}")
        logger.info(f"   Payload: {payload}")
        
        publisher.disconnect()
        
    except Exception as e:
        logger.error(f"Error sending test command: {e}")
        raise


def print_mqtt_config():
    """Print current MQTT configuration."""
    logger.info("Current MQTT Configuration:")
    logger.info(f"   Host: {device_config.mqtt_config.get('host')}")
    logger.info(f"   Port: {device_config.mqtt_config.get('port')}")
    logger.info(f"   Username: {device_config.mqtt_config.get('username')}")
    logger.info(f"   Client ID: {device_config.mqtt_config.get('client_id')}")
    logger.info(f"   Enabled: {device_config.mqtt_config.get('enabled')}")


async def main():
    """Main test function."""
    print_mqtt_config()
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1]
        
        if test_type == "connection":
            await test_mqtt_connection()
        elif test_type == "handlers":
            await test_mqtt_with_handlers()
        elif test_type == "send":
            await send_test_command()
        else:
            logger.error(f"Unknown test type: {test_type}")
            logger.info("Available test types: connection, handlers, send")
    else:
        logger.info("Available test types:")
        logger.info("  connection - Test basic MQTT connection")
        logger.info("  handlers   - Test MQTT with message handlers")
        logger.info("  send       - Send a test command")
        logger.info("")
        logger.info("Usage: python mqtt/test_mqtt.py <test_type>")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test failed: {e}")
        sys.exit(1)
