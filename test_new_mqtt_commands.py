#!/usr/bin/env python3
"""
Test script for new MQTT command structure
"""

import asyncio
import json
import logging
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_new_mqtt_commands():
    """Test new MQTT command structure."""
    try:
        from mqtt.client import AsyncMQTTClient
        from mqtt.handlers import handle_device_command
        from config import device_config
        
        logger.info("Creating MQTT client...")
        client = AsyncMQTTClient()
        
        # Set main loop
        main_loop = asyncio.get_running_loop()
        client.set_main_loop(main_loop)
        
        # Register handlers for all categories
        logger.info("Registering message handlers...")
        client.register_message_handler("devices/+/electronic/#", handle_device_command)
        client.register_message_handler("devices/+/system/#", handle_device_command)
        client.register_message_handler("devices/+/sale/#", handle_device_command)
        client.register_message_handler("devices/+/storage/#", handle_device_command)
        
        logger.info(f"Registered handlers: {list(client.message_handlers.keys())}")
        
        # Start client
        logger.info("Starting MQTT client...")
        await client.start()
        
        if client.is_connected and client.is_subscribed:
            logger.info("✓ MQTT client is ready and listening")
            logger.info("New command structure is active!")
            logger.info("")
            logger.info("Test commands you can send:")
            
            client_id = device_config.mqtt_config['client_id']
            
            logger.info("Electronic commands:")
            logger.info(f"  devices/{client_id}/electronic/section_open")
            logger.info(f"    Payload: {{\"section_id\": 1}}")
            logger.info(f"  devices/{client_id}/electronic/check_doors")
            logger.info(f"    Payload: {{\"section_id\": 1}}")
            logger.info(f"  devices/{client_id}/electronic/unlock_service")
            logger.info(f"    Payload: {{}}")
            logger.info(f"  devices/{client_id}/electronic/check_service")
            logger.info(f"    Payload: {{}}")
            logger.info("")
            
            logger.info("System commands:")
            logger.info(f"  devices/{client_id}/system/reboot_device")
            logger.info(f"    Payload: {{}}")
            logger.info("")
            
            logger.info("Sale commands:")
            logger.info(f"  devices/{client_id}/sale/edit_reservation")
            logger.info(f"    Payload: {{\"uuid\": \"test-uuid\", \"status\": 0, \"max_days\": 1}}")
            logger.info("")
            
            logger.info("Storage commands:")
            logger.info(f"  devices/{client_id}/storage/storage_edit_reservation")
            logger.info(f"    Payload: {{\"uuid\": \"test-uuid\", \"status\": 0, \"max_days\": 1}}")
            logger.info("")
            
            logger.info("Responses will be published to:")
            logger.info(f"  devices/{client_id}/[category]/[command]/response")
            logger.info("")
            logger.info("Listening for 60 seconds... Press Ctrl+C to stop")
            
            # Keep running for 60 seconds
            for i in range(60):
                await asyncio.sleep(1)
                if i % 15 == 0 and i > 0:
                    logger.info(f"Still listening... ({60-i} seconds remaining)")
                    
        else:
            logger.error("✗ MQTT client failed to initialize properly")
            
        await client.stop()
        logger.info("MQTT client stopped")
        
    except Exception as e:
        logger.error(f"Error in test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        asyncio.run(test_new_mqtt_commands())
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test failed: {e}")
        sys.exit(1)
