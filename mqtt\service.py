"""
MQTT Service Module

This module provides high-level service functions for interacting with MQTT
from other parts of the application. It acts as a bridge between the application
logic and the MQTT client.
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional
from mqtt.client import get_mqtt_client

logger = logging.getLogger(__name__)


class MQTTService:
    """High-level MQTT service for application integration."""
    
    @staticmethod
    def is_connected() -> bool:
        """Check if MQTT client is connected."""
        client = get_mqtt_client()
        return client is not None and client.is_connected
    
    @staticmethod
    async def publish_device_status(device_id: str, status: Dict[str, Any]) -> bool:
        """
        Publish device status to MQTT.
        
        Args:
            device_id: The device identifier
            status: Status information to publish
            
        Returns:
            True if published successfully, False otherwise
        """
        client = get_mqtt_client()
        if not client or not client.is_connected:
            logger.warning("MQTT client not available for status publishing")
            return False
            
        try:
            topic = f"devices/{device_id}/status"
            payload = json.dumps(status, ensure_ascii=False)
            
            info = client.client.publish(topic, payload, qos=1, retain=True)
            if info.rc == 0:
                info.wait_for_publish()
                logger.info(f"Device status published to: {topic}")
                return True
            else:
                logger.error(f"Failed to publish device status (rc: {info.rc})")
                return False
                
        except Exception as e:
            logger.error(f"Error publishing device status: {e}")
            return False
    
    @staticmethod
    async def publish_event(device_id: str, event_type: str, event_data: Dict[str, Any]) -> bool:
        """
        Publish an event to MQTT.
        
        Args:
            device_id: The device identifier
            event_type: Type of event (e.g., 'door_opened', 'transaction_completed')
            event_data: Event data to publish
            
        Returns:
            True if published successfully, False otherwise
        """
        client = get_mqtt_client()
        if not client or not client.is_connected:
            logger.warning("MQTT client not available for event publishing")
            return False
            
        try:
            topic = f"devices/{device_id}/events/{event_type}"
            
            # Add timestamp to event data
            event_data_with_timestamp = {
                **event_data,
                "timestamp": asyncio.get_event_loop().time(),
                "event_type": event_type,
                "device_id": device_id
            }
            
            payload = json.dumps(event_data_with_timestamp, ensure_ascii=False)
            
            info = client.client.publish(topic, payload, qos=1, retain=False)
            if info.rc == 0:
                info.wait_for_publish()
                logger.info(f"Event published to: {topic}")
                logger.debug(f"Event data: {event_data_with_timestamp}")

                # Log event to timeline if it's a significant event
                try:
                    from mqtt.timeline_integration import log_mqtt_event_to_timeline
                    await log_mqtt_event_to_timeline(
                        event_type=event_type,
                        event_data=event_data,
                        device_id=device_id
                    )
                except Exception as e:
                    logger.warning(f"Failed to log event to timeline: {e}")

                return True
            else:
                logger.error(f"Failed to publish event (rc: {info.rc})")
                return False
                
        except Exception as e:
            logger.error(f"Error publishing event: {e}")
            return False
    
    @staticmethod
    async def publish_notification(device_id: str, notification: Dict[str, Any]) -> bool:
        """
        Publish a notification to MQTT.
        
        Args:
            device_id: The device identifier
            notification: Notification data to publish
            
        Returns:
            True if published successfully, False otherwise
        """
        client = get_mqtt_client()
        if not client or not client.is_connected:
            logger.warning("MQTT client not available for notification publishing")
            return False
            
        try:
            topic = f"devices/{device_id}/notifications"
            
            # Add timestamp to notification
            notification_with_timestamp = {
                **notification,
                "timestamp": asyncio.get_event_loop().time(),
                "device_id": device_id
            }
            
            payload = json.dumps(notification_with_timestamp, ensure_ascii=False)
            
            info = client.client.publish(topic, payload, qos=1, retain=False)
            if info.rc == 0:
                info.wait_for_publish()
                logger.info(f"Notification published to: {topic}")
                return True
            else:
                logger.error(f"Failed to publish notification (rc: {info.rc})")
                return False
                
        except Exception as e:
            logger.error(f"Error publishing notification: {e}")
            return False
    
    @staticmethod
    async def send_command_response(device_id: str, command: str, response: Dict[str, Any]) -> bool:
        """
        Send a command response via MQTT.
        This is a convenience method that wraps the client's publish_response method.
        
        Args:
            device_id: The device identifier
            command: The command that was executed
            response: The response data
            
        Returns:
            True if sent successfully, False otherwise
        """
        client = get_mqtt_client()
        if not client:
            logger.warning("MQTT client not available for command response")
            return False
            
        return await client.publish_response(device_id, command, response)
    
    @staticmethod
    def get_connection_info() -> Dict[str, Any]:
        """
        Get MQTT connection information.
        
        Returns:
            Dictionary with connection status and details
        """
        client = get_mqtt_client()
        if not client:
            return {
                "connected": False,
                "client_available": False,
                "error": "MQTT client not initialized"
            }
            
        from config import device_config
        
        return {
            "connected": client.is_connected,
            "subscribed": client.is_subscribed,
            "client_available": True,
            "broker_host": device_config.mqtt_config.get("host"),
            "broker_port": device_config.mqtt_config.get("port"),
            "client_id": device_config.mqtt_config.get("client_id"),
            "username": device_config.mqtt_config.get("username")
        }


# Convenience functions for common operations
async def publish_door_event(device_id: str, door_id: str, action: str, **kwargs) -> bool:
    """
    Publish a door-related event.
    
    Args:
        device_id: The device identifier
        door_id: The door identifier
        action: The action performed (e.g., 'opened', 'closed', 'locked', 'unlocked')
        **kwargs: Additional event data
        
    Returns:
        True if published successfully, False otherwise
    """
    event_data = {
        "door_id": door_id,
        "action": action,
        **kwargs
    }
    
    return await MQTTService.publish_event(device_id, "door_state", event_data)


async def publish_transaction_event(device_id: str, transaction_id: str, status: str, **kwargs) -> bool:
    """
    Publish a transaction-related event.
    
    Args:
        device_id: The device identifier
        transaction_id: The transaction identifier
        status: The transaction status
        **kwargs: Additional event data
        
    Returns:
        True if published successfully, False otherwise
    """
    event_data = {
        "transaction_id": transaction_id,
        "status": status,
        **kwargs
    }
    
    return await MQTTService.publish_event(device_id, "transaction", event_data)


async def publish_storage_event(device_id: str, slot_id: str, action: str, **kwargs) -> bool:
    """
    Publish a storage-related event.
    
    Args:
        device_id: The device identifier
        slot_id: The storage slot identifier
        action: The action performed (e.g., 'reserved', 'occupied', 'released')
        **kwargs: Additional event data
        
    Returns:
        True if published successfully, False otherwise
    """
    event_data = {
        "slot_id": slot_id,
        "action": action,
        **kwargs
    }
    
    return await MQTTService.publish_event(device_id, "storage", event_data)


async def publish_error_event(device_id: str, error_type: str, error_message: str, **kwargs) -> bool:
    """
    Publish an error event.
    
    Args:
        device_id: The device identifier
        error_type: The type of error
        error_message: The error message
        **kwargs: Additional error data
        
    Returns:
        True if published successfully, False otherwise
    """
    event_data = {
        "error_type": error_type,
        "error_message": error_message,
        "severity": kwargs.get("severity", "error"),
        **kwargs
    }
    
    return await MQTTService.publish_event(device_id, "error", event_data)


# Global service instance
mqtt_service = MQTTService()
